# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-12 16:58:14

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250811_103911.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 994 条
- **vLLM 平均 e2e 时间**: 6.606s
- **Vidur 平均 e2e 时间**: 23.210s
- **整体平均差异**: 16.604s (+251.4%)

### 关键发现
- **平均绝对相对差异**: 244.5%
- **相对差异标准差**: 28.7%
- **差异范围**: +47.3% 到 +278.6%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 0 条 | 0.0% | 一般 |
| ≥ 20% | 994 条 | 100.0% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 250.6%
- **P75**: 255.5%
- **P90**: 259.2%
- **P95**: 261.1%
- **P99**: 265.9%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 244.5%）
2. **差异稳定**: 标准差 28.7%，说明偏差相对稳定
3. **影响范围**: 100.0% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 244.5% 超出理想范围。

---
*报告生成时间: 2025-08-12 16:58:14*
