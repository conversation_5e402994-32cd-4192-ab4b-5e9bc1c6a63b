# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-13 15:06:50

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250813_141120.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.788s
- **Vidur 平均 e2e 时间**: 6.703s
- **整体平均差异**: -3.085s (-31.5%)

### 关键发现
- **平均绝对相对差异**: 32.1%
- **相对差异标准差**: 6.0%
- **差异范围**: -68.6% 到 +2.0%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 1 条 | 0.1% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 1 条 | 0.1% | 一般 |
| ≥ 20% | 992 条 | 99.8% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 32.8%
- **P75**: 34.7%
- **P90**: 36.0%
- **P95**: 39.5%
- **P99**: 56.0%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 32.1%）
2. **差异稳定**: 标准差 6.0%，说明偏差相对稳定
3. **影响范围**: 99.8% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 32.1% 超出理想范围。

---
*报告生成时间: 2025-08-13 15:06:50*
