# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 10:12:45

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_100728.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 10.064s
- **Vidur 平均 e2e 时间**: 21.697s
- **整体平均差异**: 11.633s (+115.6%)

### 关键发现
- **平均绝对相对差异**: 111.0%
- **相对差异标准差**: 36.1%
- **差异范围**: -47.9% 到 +165.3%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 2 条 | 0.2% | 优秀 |
| 5-10% | 4 条 | 0.4% | 良好 |
| 10-20% | 12 条 | 1.2% | 一般 |
| ≥ 20% | 977 条 | 98.2% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 122.6%
- **P75**: 132.1%
- **P90**: 140.6%
- **P95**: 145.5%
- **P99**: 158.4%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 110.0%）
2. **差异稳定**: 标准差 36.1%，说明偏差相对稳定
3. **影响范围**: 98.2% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 111.0% 超出理想范围。

---
*报告生成时间: 2025-08-14 10:12:45*
