# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-13 14:16:56

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250813_141120.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.788s
- **Vidur 平均 e2e 时间**: 90.552s
- **整体平均差异**: 80.764s (+825.2%)

### 关键发现
- **平均绝对相对差异**: 8146.0%
- **相对差异标准差**: 31646.5%
- **差异范围**: +12.5% 到 +330952.3%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 2 条 | 0.2% | 一般 |
| ≥ 20% | 992 条 | 99.8% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 809.6%
- **P75**: 2259.3%
- **P90**: 11768.6%
- **P95**: 29899.8%
- **P99**: 202611.0%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 8146.0%）
2. **差异稳定**: 标准差 31646.5%，说明偏差相对稳定
3. **影响范围**: 99.8% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 8146.0% 超出理想范围。

---
*报告生成时间: 2025-08-13 14:16:56*
