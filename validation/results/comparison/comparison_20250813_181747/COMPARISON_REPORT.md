# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-13 18:17:50

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250813_141120.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.788s
- **Vidur 平均 e2e 时间**: 20.915s
- **整体平均差异**: 11.127s (+113.7%)

### 关键发现
- **平均绝对相对差异**: 108.1%
- **相对差异标准差**: 39.2%
- **差异范围**: -61.1% 到 +164.6%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 4 条 | 0.5% | 优秀 |
| 5-10% | 6 条 | 0.6% | 良好 |
| 10-20% | 14 条 | 1.4% | 一般 |
| ≥ 20% | 970 条 | 97.5% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 121.2%
- **P75**: 129.1%
- **P90**: 145.6%
- **P95**: 151.1%
- **P99**: 158.8%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 106.8%）
2. **差异稳定**: 标准差 39.2%，说明偏差相对稳定
3. **影响范围**: 97.5% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 108.1% 超出理想范围。

---
*报告生成时间: 2025-08-13 18:17:50*
