#!/bin/bash

# Vidur 模拟运行脚本
# 直接从配置文件加载所有参数并运行 vidur 模拟

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载通用YAML解析脚本
if [ ! -f "validation/parse_yaml.sh" ]; then
    print_error "YAML解析脚本不存在: validation/parse_yaml.sh"
    exit 1
fi

source validation/parse_yaml.sh

# 检查是否在正确的目录
if [ ! -f "vidur/main.py" ]; then
    print_error "vidur/main.py 未找到。请在 vidur 项目根目录运行此脚本。"
    exit 1
fi

# 检查配置文件
SHARED_CONFIG="validation/configs/config_shared.yaml"
VIDUR_CONFIG="validation/configs/config_vidur.yaml"

if [ ! -f "$SHARED_CONFIG" ]; then
    print_error "共享配置文件未找到: $SHARED_CONFIG"
    exit 1
fi

if [ ! -f "$VIDUR_CONFIG" ]; then
    print_error "Vidur 配置文件未找到: $VIDUR_CONFIG"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "未检测到虚拟环境。请确保已激活 Python 环境。"
    print_warning "可以使用以下命令激活:"
    print_warning "  - conda: mamba activate ./env"
    print_warning "  - venv: source .venv/bin/activate"
fi

print_info "开始运行 Vidur LLM 推理模拟..."
print_info "配置文件:"
print_info "  - 共享配置: $SHARED_CONFIG"
print_info "  - Vidur 配置: $VIDUR_CONFIG"

# 解析配置文件
print_info "解析配置文件..."
parse_yaml "$SHARED_CONFIG" "SHARED"
parse_yaml "$VIDUR_CONFIG" "VIDUR"

# 自动查找最新的 vLLM trace 文件
TRACE_FILE_DIR="$VIDUR_request_generator_config_trace_file_dir"
if [ -z "$TRACE_FILE_DIR" ]; then
    TRACE_FILE_DIR="validation/results/vllm"  # 默认值
fi
print_info "自动查找最新的 vLLM trace 文件..."
print_info "搜索目录: $TRACE_FILE_DIR"

if [ -d "$TRACE_FILE_DIR" ]; then
    # 查找最新的 vllm_results_*.csv 文件
    LATEST_TRACE_FILE=$(find "$TRACE_FILE_DIR" -name "vllm_results_*.csv" -type f -printf "%T@ %p\n" 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -n "$LATEST_TRACE_FILE" ] && [ -f "$LATEST_TRACE_FILE" ]; then
        print_info "找到最新的 trace 文件: $LATEST_TRACE_FILE"
        # 更新文件修改时间信息
        FILE_TIME=$(stat -c "%Y" "$LATEST_TRACE_FILE" 2>/dev/null)
        if [ -n "$FILE_TIME" ]; then
            FILE_DATE=$(date -d "@$FILE_TIME" "+%Y-%m-%d %H:%M:%S")
            print_info "文件修改时间: $FILE_DATE"
        fi
        # 覆盖配置文件中的 trace_file 设置
        VIDUR_request_generator_config_trace_file="$LATEST_TRACE_FILE"
        print_info "自动设置 trace_file = $LATEST_TRACE_FILE"
    else
        print_warning "未在 $TRACE_FILE_DIR 中找到 vllm_results_*.csv 文件"
        print_warning "将使用配置文件中的现有 trace_file 设置"
    fi
else
    print_warning "vLLM 结果目录不存在: $TRACE_FILE_DIR"
    print_warning "将使用配置文件中的现有 trace_file 设置"
fi

# 检查最终的 trace 文件
TRACE_FILE="$VIDUR_request_generator_config_trace_file"
if [ -n "$TRACE_FILE" ] && [ ! -f "$TRACE_FILE" ]; then
    print_warning "跟踪文件未找到: $TRACE_FILE"
    print_warning "请确保已运行 vLLM 测试并生成了跟踪文件。"
elif [ -n "$TRACE_FILE" ] && [ -f "$TRACE_FILE" ]; then
    # 显示文件基本信息
    FILE_SIZE=$(stat -c "%s" "$TRACE_FILE" 2>/dev/null)
    if [ -n "$FILE_SIZE" ]; then
        print_info "trace 文件大小: $FILE_SIZE 字节"
    fi
fi

# 调试：打印解析到的关键参数
print_info "解析到的关键参数："
print_info "  - 模型: $SHARED_model_name"
print_info "  - 设备: $SHARED_device"
print_info "  - max_num_seqs: $SHARED_max_num_seqs"
print_info "  - max_num_batched_tokens: $SHARED_max_num_batched_tokens"
print_info "  - max_tokens: $SHARED_max_tokens"
print_info "  - trace_file: $TRACE_FILE"

# 构建 vidur 命令参数
print_info "构建 vidur 命令参数..."

VIDUR_ARGS=""

# 基础配置
if [ ! -z "$SHARED_seed" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --seed $SHARED_seed"
fi
if [ ! -z "$SHARED_log_level" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --log_level $SHARED_log_level"
fi

# 模型配置 - 从共享配置
if [ ! -z "$SHARED_model_name" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_model_name $SHARED_model_name"
fi

# 并行配置
if [ ! -z "$SHARED_tensor_parallel_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_tensor_parallel_size $SHARED_tensor_parallel_size"
fi
if [ ! -z "$SHARED_pipeline_parallel_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_num_pipeline_stages $SHARED_pipeline_parallel_size"
fi

# 设备配置
if [ ! -z "$SHARED_device" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_device $SHARED_device"
fi
if [ ! -z "$SHARED_network_device" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_network_device $SHARED_network_device"
fi
if [ ! -z "$SHARED_memory_margin_fraction" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_memory_margin_fraction $SHARED_memory_margin_fraction"
fi

# 集群配置
if [ ! -z "$VIDUR_cluster_config_num_replicas" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --cluster_config_num_replicas $VIDUR_cluster_config_num_replicas"
fi

# 全局调度器配置
if [ ! -z "$VIDUR_global_scheduler_config_type" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --global_scheduler_config_type $VIDUR_global_scheduler_config_type"
fi

# 副本调度器配置 - 关键修复：确保使用正确的参数
if [ ! -z "$VIDUR_replica_scheduler_config_type" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_scheduler_config_type $VIDUR_replica_scheduler_config_type"
fi

# vLLM 调度器特定配置 - 使用共享配置中的参数
if [ ! -z "$SHARED_block_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_block_size $SHARED_block_size"
fi
if [ ! -z "$SHARED_max_num_seqs" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_batch_size_cap $SHARED_max_num_seqs"
    print_info "设置 batch_size_cap = $SHARED_max_num_seqs (对应 vLLM max_num_seqs)"
fi
if [ ! -z "$SHARED_max_num_batched_tokens" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_max_tokens_in_batch $SHARED_max_num_batched_tokens"
    print_info "设置 max_tokens_in_batch = $SHARED_max_num_batched_tokens (对应 vLLM max_num_batched_tokens)"
fi
if [ ! -z "$SHARED_watermark_blocks_fraction" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_watermark_blocks_fraction $SHARED_watermark_blocks_fraction"
fi

# 请求生成器配置
if [ ! -z "$VIDUR_request_generator_config_type" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --request_generator_config_type $VIDUR_request_generator_config_type"
fi

# trace_replay 请求生成器特定配置
if [ "$VIDUR_request_generator_config_type" = "trace_replay" ]; then
    if [ ! -z "$TRACE_FILE" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_trace_file $TRACE_FILE"
    fi
    if [ ! -z "$VIDUR_request_generator_config_prefill_scale_factor" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_prefill_scale_factor $VIDUR_request_generator_config_prefill_scale_factor"
    fi
    if [ ! -z "$VIDUR_request_generator_config_decode_scale_factor" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_decode_scale_factor $VIDUR_request_generator_config_decode_scale_factor"
    fi
    # 提取纯数字值，去除注释
    TIME_SCALE_VALUE=$(echo "$VIDUR_request_generator_config_time_scale_factor" | grep -o '^[0-9.]\+' | head -1)
    if [ ! -z "$TIME_SCALE_VALUE" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_time_scale_factor $TIME_SCALE_VALUE"
    fi
    # max_tokens 使用共享配置中的值
    # 需要从 SHARED_max_tokens 中提取纯数字值
    MAX_TOKENS_VALUE=$(echo "$SHARED_max_tokens" | grep -o '[0-9]\+' | head -1)
    if [ ! -z "$MAX_TOKENS_VALUE" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_max_tokens $MAX_TOKENS_VALUE"
        print_info "设置 trace max_tokens = $MAX_TOKENS_VALUE"
    fi
fi

# 执行时间预测器配置
if [ ! -z "$VIDUR_execution_time_predictor_config_type" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --execution_time_predictor_config_type $VIDUR_execution_time_predictor_config_type"
fi

# 使用共享配置中的执行时间预测器参数
if [ ! -z "$SHARED_execution_time_predictor_type" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --execution_time_predictor_config_type $SHARED_execution_time_predictor_type"
fi
if [ ! -z "$SHARED_execution_time_predictor_prediction_max_prefill_chunk_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size $SHARED_execution_time_predictor_prediction_max_prefill_chunk_size"
fi
if [ ! -z "$SHARED_execution_time_predictor_prediction_max_batch_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_batch_size $SHARED_execution_time_predictor_prediction_max_batch_size"
fi
if [ ! -z "$SHARED_execution_time_predictor_prediction_max_tokens_per_request" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request $SHARED_execution_time_predictor_prediction_max_tokens_per_request"
fi

# 设置默认的执行时间预测器参数（如果共享配置中没有）
VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size 4096"
VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_batch_size 512"
VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request 4096"

# 其他执行时间预测器配置
if [ ! -z "$VIDUR_execution_time_predictor_config_k_fold_cv_splits" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_k_fold_cv_splits $VIDUR_execution_time_predictor_config_k_fold_cv_splits"
fi
if [ ! -z "$VIDUR_execution_time_predictor_config_no_cache" ]; then
    if [ "$VIDUR_execution_time_predictor_config_no_cache" = "true" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_no_cache"
    fi
fi

# CPU开销建模配置 - 关键修复：添加对 skip_cpu_overhead_modeling 参数的支持
if [ ! -z "$VIDUR_execution_time_predictor_config_skip_cpu_overhead_modeling" ]; then
    if [ "$VIDUR_execution_time_predictor_config_skip_cpu_overhead_modeling" = "true" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling"
        print_info "启用跳过CPU开销建模 (只使用GPU计算时间)"
    else
        VIDUR_ARGS="$VIDUR_ARGS --no-random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling"
        print_info "启用CPU开销建模 (包含完整的执行时间预测)"
    fi
fi

# 指标配置
if [ ! -z "$VIDUR_metrics_config_output_dir" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --metrics_config_output_dir $VIDUR_metrics_config_output_dir"
fi
if [ ! -z "$VIDUR_metrics_config_cache_dir" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --metrics_config_cache_dir $VIDUR_metrics_config_cache_dir"
fi

# 布尔型指标配置
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_write_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_enable_chrome_trace"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_plots"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_request_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_batch_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_utilization_metrics"

# 时间限制
if [ ! -z "$VIDUR_time_limit" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --time_limit $VIDUR_time_limit"
fi

# 运行 vidur 模拟
print_info "启动 Vidur 模拟..."
FULL_COMMAND="python -m vidur.main $VIDUR_ARGS"
print_info "命令: $FULL_COMMAND"

python -m vidur.main $VIDUR_ARGS

# 检查执行结果
if [ $? -eq 0 ]; then
    print_info "Vidur 模拟完成成功！"
    
    # 查找最新的输出目录
    if [ ! -z "$OUTPUT_DIR" ] && [ -d "$OUTPUT_DIR" ]; then
        print_info "输出目录: $OUTPUT_DIR"
        LATEST_OUTPUT=$(ls -t "$OUTPUT_DIR"/ 2>/dev/null | head -n1)
        if [ -n "$LATEST_OUTPUT" ]; then
            print_info "最新输出: $OUTPUT_DIR/$LATEST_OUTPUT"
        fi
    else
        # 检查默认的 simulator_output 目录
        if [ -d "simulator_output" ]; then
            LATEST_OUTPUT=$(ls -t simulator_output/ 2>/dev/null | head -n1)
            if [ -n "$LATEST_OUTPUT" ]; then
                print_info "最新输出目录: simulator_output/$LATEST_OUTPUT"
            fi
        fi
    fi
else
    print_error "Vidur 模拟失败，退出码: $?"
    exit 1
fi